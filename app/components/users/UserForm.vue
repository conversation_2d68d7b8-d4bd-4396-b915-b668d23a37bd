<script setup lang="ts">
import { z } from 'zod'
import { faker<PERSON><PERSON> as faker } from '@faker-js/faker'
import type UserTypesSelect from './UserTypesSelect.vue'
import type { FormError, FormSubmitEvent } from '#ui/types'
import { UserType } from '~/types/index.d'

const schema = z.object({
  email: z.string().email('無効なメールアドレスです。'),
  username: z
    .string()
    .min(3, 'ユーザ名は3文字以上で入力してください。')
    .max(100, 'ユーザ名は100文字以下で入力してください。'),
  password: z
    .union([
      z
        .string()
        .min(8, 'パスワードは8文字以上で入力してください。')
        .max(20, 'パスワードは20文字以下で入力してください。'),
      z.string().optional()
    ])
    .superRefine((val, ctx) => {
      if (!props.isUpdateMode) {
        if (!val) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'パスワードは必須です。'
          })
        } else {
          if (val.length < 8) {
            ctx.addIssue({
              code: z.ZodIssueCode.too_small,
              message: 'パスワードは8文字以上で入力してください。',
              minimum: 8,
              inclusive: true,
              type: 'string'
            })
          }
          if (val.length > 20) {
            ctx.addIssue({
              code: z.ZodIssueCode.too_big,
              message: 'パスワードは20文字以下で入力してください。',
              maximum: 20,
              inclusive: true,
              type: 'string'
            })
          }
        }
      }
    }),
  display_name: z.string().optional(),
  user_type: z.enum([UserType.STAFF, UserType.ADMIN, UserType.PNL_ADMIN])
})
type Schema = z.output<typeof schema>

const emit = defineEmits(['close', 'submit'])
const props = defineProps<{
  user: Schema
  loading?: boolean
  error: any
  email?: string
  display_name?: string
  label?: string
  username?: string
  user_type?: UserType
  isUpdateMode: {
    type: boolean
    default: false
  }
}>()

const state = reactive({
  email: '',
  username: '',
  display_name: '',
  password: '',
  user_type: 'staff'
})

async function onSubmit(event: FormSubmitEvent<Schema>) {
  // Do something with data
  if (event.data.display_name === '') {
    event.data.display_name = null
  }
  emit('submit', event.data)
  // emit('close')
}

onMounted(() => {
  // if (props.labelKey) {
  //   state.labelKey = props.labelKey
  // }
  if (props.label) {
    state.label = props.label
  }
  if (props.email) {
    state.email = props.email
  }
  if (props.display_name) {
    state.display_name = props.display_name
  }
  if (props.username) {
    state.username = props.username
  }
  if (props.user_type) {
    state.user_type = props.user_type
  }
})

const formRef = ref()
// watch props.error
watch(
  () => props.error,
  (error) => {
    if (error) {
      formRef.value?.setErrors([
        { path: 'label', message: error?.error_message }
      ])
    }
  }
)

const generateTempPassword = () => {
  state.password = faker.internet.password({
    pattern: /[A-Za-z0-9!@#$%^&*()_+]/,
    prefix: 'Pnl@1'
  })
}
</script>

<template>
  <UForm
    ref="formRef"
    :schema="schema"
    :state="state"
    class="space-y-4"
    autocomplete="off"
    @submit="onSubmit"
  >
    <UFormGroup
      label="メールアドレス"
      name="email"
      required
    >
      <UInput
        v-model="state.email"
        type="email"
        placeholder="例: <EMAIL>"
        autofocus
        aria-autocomplete="none"
        autocomplete="new-email"
      />
    </UFormGroup>
    <UFormGroup
      label="ユーザ名"
      name="username"
      required
    >
      <UInput
        v-model="state.username"
        type="text"
        placeholder="ログインのユーザ名"
        aria-autocomplete="none"
        autocomplete="new-username"
        :disabled="isUpdateMode"
      />
    </UFormGroup>

    <UFormGroup
      v-if="!isUpdateMode"
      label="仮パスワード"
      name="password"
      required
    >
      <template #hint>
        <div
          class="flex items-center gap-1 text-sm text-primary-500 hover:underline cursor-pointer dark:text-primary-400"
          data-tour="user-generate-password"
          @click="generateTempPassword"
        >
          仮パスワードを生成する
        </div>
      </template>
      <BasePasswordInput
        v-model="state.password"
        show-password
        placeholder="ユーザの仮パスワード"
        aria-autocomplete="none"
        autocomplete="new-password"
      />
    </UFormGroup>

    <UFormGroup
      label="名前"
      name="display_name"
    >
      <UInput
        v-model="state.display_name"
        type="text"
        placeholder="ユーザの名前"
        aria-autocomplete="none"
        autocomplete="new-name"
      />
    </UFormGroup>
    <UFormGroup
      label="ユーザ権限"
      name="user_type"
    >
      <UserTypesSelect
        v-model="state.user_type"
        type="text"
        aria-autocomplete="none"
        autocomplete="off"
        :disabled="isUpdateMode"
      />
    </UFormGroup>
    <BasicFormButtonGroup
      class="justify-end"
      :loading="props.loading"
      @close="emit('close')"
    />
  </UForm>
</template>
