<template>
  <div class="flex flex-col gap-4">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2">
      <div class="flex flex-col gap-2 justify-between">
        <DashboardStatisticsCard
          data-tour="dashboard-statistics-card-total-questions"
          v-bind="statisticsData[0]"
          :range="range"
          :loading="loadings.getReportSummary"
          class="h-full hover:shadow-lg cursor-pointer"
          @click="onGoToLogsWithCurrentRange()"
        />
        <div class="h-full flex flex-row gap-2 items-center group">
          <DashboardStatisticsCard
            data-tour="dashboard-statistics-card-unanswered-questions"
            v-bind="statisticsData[1]"
            :range="range"
            :loading="loadings.getReportSummary"
            class="h-full hover:shadow-lg cursor-pointer flex-1 transition-all duration-200"
            @click="goto('statistics/unanswered-questions/')"
          />
          <UIcon
            name="ep:right"
            class="text-gray-500 dark:text-gray-400 text-xl w-0 -ml-2 group-hover:ml-0 group-hover:relative group-hover:w-6 transition-all duration-200"
          />
        </div>
      </div>
      <div>
        <DashboardStatisticsUnansweredCard
          data-tour="dashboard-statistics-unanswered-reasons"
          :unanswered="reportSummary?.unanswered"
          :loading="loadings.getReportSummary"
          class="h-full"
          @click="goto('statistics/unanswered-questions/')"
        />
      </div>
      <div>
        <DashboardStatisticsChartCard
          data-tour="dashboard-statistics-daily-response-rate"
          :items="chartItems"
          :rate="
            (responseRateSummary.success / responseRateSummary.total) * 100
          "
          :loading="loadings.getDailyResponseRate"
          class="h-full"
        />
      </div>
      <div>
        <DashboardStatisticsChartCard
          data-tour="dashboard-statistics-survey-reports"
          :items="surveyReportsChartDataset"
          title="アンケートの割合"
          :loading="loadings.getSurveyReport"
          class="h-full"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import colors from 'tailwindcss/colors'

const logsStore = useLogsStore()

const { logsFilter } = storeToRefs(logsStore)
const reportsStore = useReportsStore()
const {
  responseRateSummary,
  surveyReportsChartDataset,
  reportSummary,
  loadings
} = storeToRefs(reportsStore)
const statisticsData = computed(() => {
  return [
    {
      title: '全質問数',
      value: reportSummary.value?.total,
      unit: '件',
      color: 'sky',
      icon: 'mdi:frequently-asked-questions',
      tooltip: 'チャットボットへの質問数'
    },
    {
      title: '未回答数',
      value: reportSummary.value?.unanswered?.total,
      unit: '件',
      color: 'red',
      icon: 'teenyicons:message-no-access-outline',
      tooltip: '回答が入力されていないモノ'
    }
  ]
})

const chartItems = computed(() => {
  return [
    {
      name: '回答数',
      value: responseRateSummary.value?.success,
      color: colors['sky'][500]
    },
    {
      name: '未回答数',
      value: reportSummary.value?.unanswered?.total,
      color: colors['red'][500]
    }
  ]
})

const props = defineProps({
  range: Object
})

const goto = (path: string) => {
  const { selectedTenantId, selectedEnvId } = useApp()
  navigateTo(`/${selectedTenantId.value}/${selectedEnvId.value}/${path}`)
}

const onGoToLogsWithCurrentRange = () => {
  logsFilter.value.range = props.range as any
  goto('logs')
}
</script>
