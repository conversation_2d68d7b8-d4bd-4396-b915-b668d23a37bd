<script lang="ts" setup>
import { debounce } from 'lodash'

const props = defineProps<{
  deployLogs: any[]
  loadings?: any
  deployLogPagination?: any
  deployLogFilter?: any
  totalCount: number
  onFetchLogs: () => Promise<boolean>
}>()

const {
  deployLogPagination = ref({}),
  deployLogFilter = ref({}),
  loadings = ref({}),
  totalCount = ref(0)
} = toRefs(props)

function getBadgeColor(status: string) {
  const colorMap = {
    INFO: 'green',
    WARNING: 'yellow',
    ERROR: 'red'
  }
  return colorMap[status] || 'neutral'
}
const defaultLogType = ref(['INFO', 'WARNING', 'ERROR'])

const defaultColumns = [
  {
    key: 'level',
    label: 'レベル',
    sortable: true
  },
  {
    key: 'message',
    label: 'メッセージ',
    sortable: true
  },
  {
    key: 'error',
    label: 'エラー',
    sortable: true
  },
  {
    key: 'job_id',
    label: 'ジョブID',
    sortable: true
  },
  {
    key: 'timestamp',
    label: '日時',
    sortable: true
  },
  {
    key: 'source_env_id',
    label: 'ソース環境',
    sortable: true
  },
  {
    key: 'target_env_id',
    label: '対象環境',
    sortable: true
  }
]
const expand = ref({
  openedRows: [],
  row: {}
})
const debouncedSearch = debounce(() => {
  props.onFetchLogs()
}, 500)
watch(
  () => deployLogPagination.value.pageCount,
  () => {
    deployLogPagination.value.page = 1
  }
)

watch(
  () => deployLogPagination.value,
  () => {
    props.onFetchLogs()
  },
  { deep: true }
)

watch(
  () => [deployLogFilter.value.message, deployLogFilter.value.error, deployLogFilter.value.level],
  () => {
    debouncedSearch()
  }
)
</script>

<template>
  <UModal
    :ui="{ width: 'sm:max-w-[90vw]' }"
  >
    <UCard
      :ui="{
        base: '',
        ring: '',
        divide: 'divide-y divide-gray-200 dark:divide-gray-700',
        header: { padding: '!px-3 !py-3' },
        body: {
          padding: '',
          base: 'divide-y divide-gray-200 dark:divide-gray-700'
        },
        footer: { padding: 'p-4' }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h2
            class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
          >
            デプロイログ
            <UBadge
              v-if="totalCount > 0"
              :label="totalCount"
            />
          </h2>
        </div>
      </template>
      <div class="flex items-center justify-between gap-3 px-4 py-3">
        <div class="flex items-center gap-3">
          <UInput
            v-model="deployLogFilter.message"
            icon="i-heroicons-magnifying-glass-20-solid"
            placeholder="メッセージ..."
            @keydown.esc="$event.target.blur()"
          />
          <UInput
            v-model="deployLogFilter.error"
            icon="i-heroicons-magnifying-glass-20-solid"
            placeholder="エラー..."
            @keydown.esc="$event.target.blur()"
          />
          <USelectMenu
            v-model="deployLogFilter.level"
            icon="i-heroicons-check-circle"
            placeholder="権限"
            class="w-40"
            size="md"
            :options="defaultLogType"
            :ui-menu="{ option: { base: 'capitalize' } }"
          >
            <template
              #label
            >
              <div v-if="deployLogFilter.level">
                {{ deployLogFilter.level }}
              </div>
              <div v-else>
                ログタイプ
              </div>
            </template>
            <template
              v-if="deployLogFilter.level"
              #trailing
            >
              <UButton
                v-if="defaultLogType"
                size="xs"
                icon="i-lucide-delete"
                color="gray"
                :class="[
                  'ml-2 px-2 py-1 rounded  hover:text-red-600 !pointer-events-auto'
                ]"
                @click.stop="
                  () => {
                    deployLogFilter.level = null;
                  }
                "
              />
            </template>
          </USelectMenu>
        </div>
        <div class="flex items-center gap-1.5">
          <UButton
            data-tour="deploymentlogs-reload"
            icon="prime:sync"
            color="gray"
            size="sm"
            @click="props.onFetchLogs()"
          />
        </div>
      </div>
      <UTable
        v-model:expand="expand"
        :rows="deployLogs"
        :columns="defaultColumns"
        :loading="loadings"
        class="flex-1"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #expand="{ row }">
          <div class="p-2 text-xs text-gray-500 dark:text-gray-500">
            <pre>{{ row.message || "" }}</pre>
          </div>
        </template>
        <template #timestamp-data="{ row }">
          <div class="text-sm">
            {{ formatDateTime(row.timestamp) }}
          </div>
        </template>
        <template #message-data="{ row }">
          <div class=" overflow-hidden text-ellipsis  min-w-[150px] max-w-[500px]">
            {{ row.message }}
          </div>
        </template>
        <template #level-data="{ row }">
          <div>
            <UBadge
              :label="row.level"
              class="capitalize"
              variant="subtle"
              size="sm"
              :color="getBadgeColor(row.level)"
            />
          </div>
        </template>
      </UTable>
      <template #footer>
        <div
          class="flex flex-wrap justify-between items-center"
          data-tour="deploymentlogs-footer"
        >
          <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">表示件数:</span>
            <USelect
              v-model="deployLogPagination.pageCount"
              :options="[3, 5, 10, 20, 30, 40]"
              class="w-20"
            />
          </div>

          <UPagination
            v-model="deployLogPagination.page"
            :page-count="deployLogPagination.pageCount"
            :total="totalCount"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </div>
      </template>
    </UCard>
  </UModal>
</template>

