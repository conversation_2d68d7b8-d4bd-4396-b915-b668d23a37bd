import { cloneDeep } from 'lodash'

export const useInfraGoogleCustomSearchStore = defineStore('infraGoogleCustomSearchStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    settings: [] as any[],
    originalSettings: [] as any[],
    settingFields: [
      {
        key: 'category',
        label: 'Category',
        required: true,
        type: 'text'
      },
      {
        key: 'description',
        label: 'Description',
        required: false,
        type: 'textarea'
      },
      {
        key: 'api_key',
        label: 'API Key',
        required: true,
        type: 'password'
      },
      {
        key: 'cse_id',
        label: 'CSE ID',
        required: true,
        type: 'text'
      },
      {
        key: 'language',
        label: 'Language',
        required: true,
        type: 'select',
        options: [
          { label: '日本語', value: 'lang_ja' },
          { label: '英語', value: 'lang_en' }
        ]
      },
      {
        key: 'safe_search',
        label: 'Safe Search',
        required: false,
        type: 'boolean'
      },
      {
        key: 'deep_scrapping',
        label: 'Deep Scrapping',
        required: false,
        type: 'select',
        options: [
          { label: 'Disabled', value: 0 },
          { label: 'Enabled', value: 1 },
          { label: 'Up to 5 seconds', value: 2 }
        ]
      },
      {
        key: 'use_limit',
        label: 'Use Limit',
        required: false,
        type: 'radio',
        options: [
          {
            label: 'Free (Setting will not be used if daily usage of API key > 100)',
            value: 0
          },
          {
            label: 'Paid (Setting will not be used if daily usage of API key > 10000)',
            value: 1
          }
        ]
      },
      {
        key: 'enabled',
        label: 'Enabled',
        required: false,
        type: 'boolean'
      },
      {
        key: 'extra_settings',
        label: 'Extra Settings',
        required: false,
        type: 'json'
      },
      {
        key: 'id',
        label: 'ID',
        readonly: true
      },
      {
        key: 'env_id',
        label: 'Env ID',
        readonly: true
      },
      {
        key: 'tenant_id',
        label: 'テナントID',
        readonly: true
      },
      {
        key: 'created_username',
        label: '作成者',
        readonly: true
      },
      {
        key: 'created_at',
        label: '作成日時',
        readonly: true
      },
      {
        key: 'updated_username',
        label: '更新者',
        readonly: true
      },
      {
        key: 'updated_at',
        label: '更新日時',
        readonly: true
      }
    ] as Record<string, any>[]
  }),
  getters: {
    editableSettingFields(): any[] {
      return this.settingFields.filter((field: any) => !field.readonly)
    },
    readonlySettingFields(): any[] {
      return this.settingFields.filter((field: any) => field.readonly)
    }
  },
  actions: {
    async getAllSettings(tenant_id: string, env_id: string) {
      try {
        this.loadings.getAllSettings = true
        this.errors.getAllSettings = null
        const res = await useAPI().adminService.get(
          `/v2/infra/googleCustomSearch/all/tenants/${tenant_id}/env/${env_id}`
        )
        this.settings = (res.data.settings || []).map((setting: any) => {
          setting.extra_settings = JSON.stringify(setting.extra_settings, null)
          // Ensure number fields are properly typed
          setting.deep_scrapping = Number(setting.deep_scrapping) || 0
          setting.use_limit = Number(setting.use_limit) || 0
          return setting
        })
        this.originalSettings = cloneDeep(this.settings || [])
        return res.data.settings
      } catch (error: any) {
        this.errors.getAllSettings = error?.response?.data || error
        this.settings = []
        this.originalSettings = []
        return false
      } finally {
        this.loadings.getAllSettings = false
      }
    },

    async createSetting(
      tenant_id: string,
      env_id: string,
      payload: any
    ) {
      try {
        this.loadings.createSetting = true
        this.errors.createSetting = null

        // Process extra_settings
        const processedPayload = { ...payload }
        if (processedPayload.extra_settings && typeof processedPayload.extra_settings === 'string') {
          try {
            processedPayload.extra_settings = JSON.parse(processedPayload.extra_settings)
          } catch {
            processedPayload.extra_settings = {}
          }
        }

        const response = await useAPI().adminService.post(
          `/v2/infra/googleCustomSearch/tenants/${tenant_id}/env/${env_id}`,
          processedPayload
        )
        // Add the new setting to the list
        // this.settings.push(response.data)
        this.originalSettings = cloneDeep(this.settings)
        return response.data
      } catch (error: any) {
        this.errors.createSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.createSetting = false
        this.getAllSettings(tenant_id, env_id)
      }
    },

    async updateSetting(
      id: string,
      tenant_id: string,
      env_id: string,
      payload: any
    ) {
      try {
        this.loadings.updateSetting = true
        this.errors.updateSetting = null

        // Process extra_settings
        const processedPayload = { ...payload }
        if (processedPayload.extra_settings && typeof processedPayload.extra_settings === 'string') {
          try {
            processedPayload.extra_settings = JSON.parse(processedPayload.extra_settings)
          } catch {
            processedPayload.extra_settings = {}
          }
        }

        const response = await useAPI().adminService.put(
          `/v2/infra/googleCustomSearch/${id}/tenants/${tenant_id}/env/${env_id}`,
          processedPayload
        )
        // Update the setting in the list
        const index = this.settings.findIndex(s => s.id === id)
        if (index !== -1) {
          this.settings[index] = response.data
          this.originalSettings = cloneDeep(this.settings)
        }
        return response.data
      } catch (error: any) {
        this.errors.updateSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateSetting = false
        this.getAllSettings(tenant_id, env_id)
      }
    },

    async deleteSetting(
      id: string,
      tenant_id: string,
      env_id: string
    ) {
      try {
        this.loadings.deleteSetting = true
        this.errors.deleteSetting = null
        await useAPI().adminService.delete(
          `/v2/infra/googleCustomSearch/${id}/tenants/${tenant_id}/env/${env_id}`
        )
        // Remove the setting from the list
        this.settings = this.settings.filter(s => s.id !== id)
        this.originalSettings = cloneDeep(this.settings)
        return true
      } catch (error: any) {
        this.errors.deleteSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteSetting = false
      }
    },

    createNewSetting() {
      return {
        category: '',
        description: '',
        api_key: '',
        cse_id: '',
        language: 'lang_ja',
        safe_search: true,
        deep_scrapping: 0,
        use_limit: 0,
        enabled: true,
        extra_settings: '{}',
        isEditing: true
      }
    },

    // Helper method to format extra_settings for display
    formatExtraSettingsForDisplay(settings: any) {
      if (typeof settings === 'object' && settings !== null) {
        return JSON.stringify(settings, null, 2)
      }
      return settings || '{}'
    },

    // Helper method to prepare settings for editing
    prepareSettingForEdit(setting: any) {
      const editableSetting = { ...setting }
      if (editableSetting.extra_settings && typeof editableSetting.extra_settings === 'object') {
        editableSetting.extra_settings = JSON.stringify(editableSetting.extra_settings, null, 2)
      }
      return editableSetting
    },

    resetErrors() {
      this.errors = {}
    }
  }
})
