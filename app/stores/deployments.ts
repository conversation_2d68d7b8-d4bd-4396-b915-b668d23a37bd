import { orderBy } from 'lodash'
import { sub } from 'date-fns'
import type { Range } from '~/types'

export const useDeploymentsStore = defineStore('deploymentsStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    deploymentHistories: [] as any[],
    deploymentLogs: [] as any[],
    swapHistories: [] as any[],
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    deploymentLogModal: false,
    latestDeployment: {} as any,
    deployLogFilter: {} as Record<string, any>,
    deployLogTotal: 0,
    deploymentHistoryPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    deploymentLogPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    swapHistoryPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    deploymentHistoryPageTotal: 0,
    deploymentHistoryTotalCount: 0,
    swapHistoryPageTotal: 0,
    swapHistoryTotalCount: 0,
    selectedHistoryTab: 0, // 0 for swap history, 1 for sync history
    range: {
      start: sub(new Date(), { days: 14 }),
      end: new Date()
    } as Range,
    originalSwapHistories: null as any,
    originalDeploymentHistories: null as any,
    originalDeploymentLogs: null as any
  }),
  getters: {
    from_date(): string {
      return formatDateTimeForAPI(this.range.start)
    },
    to_date(): string {
      return formatDateTimeForAPI(this.range.end)
    }
  },
  actions: {
    async fetchDeploymentHistories(tenant_id: string) {
      try {
        this.loadings.fetchDeploymentHistories = true
        this.errors.fetchDeploymentHistories = null
        this.deploymentHistories = []
        this.deploymentHistoryPageTotal = 0
        this.deploymentHistoryTotalCount = 0
        const params = {
          page: this.deploymentHistoryPagination.page,
          page_size: this.deploymentHistoryPagination.pageCount,
          order: this.deploymentHistoryPagination.asc,
          created_at_from_date: this.from_date,
          created_at_to_date: this.to_date
        } as Record<string, any>
        const response = await useAPI().adminService.get(
          `/v2/deploy/all/tenants/${tenant_id}`,
          {
            params
          }
        )
        this.deploymentHistories = orderBy(response.data?.histories || [], [
          'created_at'
        ])
        this.deploymentHistories = response.data?.histories
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.deploymentHistoryPageTotal
          = pagination?.total_pages * this.deploymentHistoryPagination.pageCount
        this.deploymentHistoryTotalCount = pagination?.total_count
        return response.data?.deployments
      } catch (error: any) {
        this.errors.fetchDeploymentHistories = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchDeploymentHistories = false
      }
    },

    async swapEnv(tenant_id: string, env_id: string) {
      try {
        this.loadings.swapEnv = true
        this.errors.swapEnv = null
        const response = await useAPI().adminService.put(
          `/v2/tenants/${tenant_id}/env/${env_id}/promote`
        )
        return response.data
      } catch (error: any) {
        this.errors.swapEnv = error?.response?.data || error
        return false
      } finally {
        this.loadings.swapEnv = false
      }
    },
    async syncProdEnvToDev(tenant_id: string, env_id: string) {
      try {
        this.loadings.syncProdEnvToDev = true
        this.errors.syncProdEnvToDev = null
        const response = await useAPI().adminService.post(
          `/v2/deploy/tenants/${tenant_id}/env/${env_id}`
        )
        this.latestDeployment = response.data
        return response.data
      } catch (error: any) {
        this.errors.syncProdEnvToDev = error?.response?.data || error
        return false
      } finally {
        this.loadings.syncProdEnvToDev = false
      }
    },
    async cancelSyncProdEnvToDev(tenant_id: string) {
      try {
        this.loadings.cancelSyncProdEnvToDev = true
        this.errors.cancelSyncProdEnvToDev = null
        const response = await useAPI().adminService.delete(
          `/v2/deploy/tenants/${tenant_id}`
        )
        this.latestDeployment = {
          ...this.latestDeployment,
          status: 3
        }
        return response.data
      } catch (error: any) {
        this.errors.cancelSyncProdEnvToDev = error?.response?.data || error
        return false
      } finally {
        this.loadings.cancelSyncProdEnvToDev = false
      }
    },
    async getLatestDeployment(tenant_id: string) {
      try {
        this.loadings.getLatestDeployment = true
        this.errors.getLatestDeployment = null
        const response = await useAPI().adminService.get(
          `/v2/deploy/latest/tenants/${tenant_id}`
        )
        this.latestDeployment = response.data
        return response.data
      } catch (error: any) {
        this.errors.getLatestDeployment = error?.response?.data || error
        return false
      } finally {
        this.loadings.getLatestDeployment = false
      }
    },
    async getDeploymentLogs(tenant_id: string, deployment_id: string) {
      try {
        this.loadings.getDeploymentLogs = true
        this.errors.getDeploymentLogs = null
        this.deploymentLogs = []
        this.deployLogTotal = 0
        const params = {
          page: this.deploymentLogPagination.page,
          page_size: this.deploymentLogPagination.pageCount,
          order: this.deploymentLogPagination.asc,
          message: this.deployLogFilter.message || null,
          error: this.deployLogFilter.error || null,
          level: this.deployLogFilter.level || null
        } as Record<string, any>
        const response = await useAPI().adminService.get(
          `/v2/deploy/${deployment_id}/logs/tenants/${tenant_id}`,
          {
            params
          }
        )
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.deployLogTotal = pagination?.total_count
        this.deploymentLogs = response.data?.logs || []
        return true
      } catch (error: any) {
        this.errors.getDeploymentLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.getDeploymentLogs = false
      }
    },
    async rollbackStagingVersion(tenant_id: string, env_id: string) {
      try {
        this.loadings.rollbackStagingVersion = true
        this.errors.rollbackStagingVersion = null
        const response = await useAPI().adminService.post(
          `/v2/deploy/revoke/tenants/${tenant_id}/env/${env_id}`
        )
        // reload page
        window.location.reload()
        return response.data
      } catch (error: any) {
        this.errors.rollbackStagingVersion = error?.response?.data || error
        return false
      } finally {
        this.loadings.rollbackStagingVersion = false
      }
    },

    async fetchSwapHistories(tenant_id: string) {
      try {
        this.loadings.fetchSwapHistories = true
        this.errors.fetchSwapHistories = null
        this.swapHistories = []
        this.swapHistoryPageTotal = 0
        this.swapHistoryTotalCount = 0
        const params = {
          page: this.swapHistoryPagination.page,
          page_size: this.swapHistoryPagination.pageCount,
          order: this.swapHistoryPagination.asc,
          from_date: this.from_date,
          to_date: this.to_date
        } as Record<string, any>

        // TODO: Replace with actual swap history API endpoint when available
        // For now, using a placeholder endpoint - this should be updated when the backend provides the actual API
        const response = await useAPI().adminService.get(
          `v2/tenants/${tenant_id}/promote/histories/all`,
          {
            params
          }
        )

        this.swapHistories = response.data?.histories || []
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.swapHistoryPageTotal
          = pagination?.total_pages * this.swapHistoryPagination.pageCount
        this.swapHistoryTotalCount = pagination?.total_count
        return response.data?.histories
      } catch (error: any) {
        this.errors.fetchSwapHistories = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchSwapHistories = false
      }
    },
    addDeploymentHistoriesDemoData() {
      this.originalDeploymentHistories = {
        deploymentHistories: this.deploymentHistories,
        deploymentHistoryPageTotal: this.deploymentHistoryPageTotal,
        deploymentHistoryTotalCount: this.deploymentHistoryTotalCount
      }
      this.deploymentHistories = [
        {
          id: 365,
          tenant_id: 'test_tenant',
          source_env_id: 'デモ環境',
          target_env_id: 'リリース環境',
          source_version: 36,
          target_version: 37,
          status: 2,
          result: true,
          updated_details: 'Success knowledge count = 1, Failed knowledge count = 0',
          error_details: null,
          log_path: 'XXX-XXXX-XXXX-XXXX-XXXX',
          created_username: 'tanaka',
          cancelled_username: null,
          created_at: new Date().toISOString(),
          started_at: new Date().toISOString(),
          finished_at: new Date().toISOString(),
          cancelled_at: null,
          isDemo: true
        },
        {
          id: 364,
          tenant_id: 'test_tenant',
          source_env_id: 'デモ環境',
          target_env_id: 'リリース環境',
          source_version: 36,
          target_version: 35,
          status: 2,
          result: true,
          updated_details: 'Success knowledge count = 2, Failed knowledge count = 0',
          error_details: null,
          log_path: 'XXX-XXXX-XXXX-XXXX-XXXX',
          created_username: 'tanaka',
          cancelled_username: 'midori',
          created_at: new Date().toISOString(),
          started_at: new Date().toISOString(),
          finished_at: new Date().toISOString(),
          cancelled_at: new Date().toISOString(),
          isDemo: true
        },
        {
          id: 331,
          tenant_id: 'test_tenant',
          source_env_id: 'pnl_playground001',
          target_env_id: 'test_env',
          source_version: 35,
          target_version: 36,
          status: 2,
          result: false,
          updated_details: 'Success knowledge count = 4, Failed knowledge count = 0',
          error_details: null,
          log_path: 'XXX-XXXX-XXXX-XXXX-XXXX',
          created_username: 'tanaka',
          cancelled_username: null,
          created_at: new Date().toISOString(),
          started_at: new Date().toISOString(),
          finished_at: new Date().toISOString(),
          cancelled_at: null,
          isDemo: true
        }
      ]
      this.deploymentHistoryPageTotal = 2
      this.deploymentHistoryTotalCount = 3
    },
    addSwapHistoriesDemoData() {
      this.originalSwapHistories = {
        swapHistories: this.swapHistories,
        swapHistoryPageTotal: this.swapHistoryPageTotal,
        swapHistoryTotalCount: this.swapHistoryTotalCount
      }
      this.swapHistories = [
        {
          id: '19b1f50c-b0ab-45a3-830d-e64727ca85ba',
          env_id: 'test_environment',
          tenant_id: 'test_tenant',
          version: 1,
          created_username: 'tanaka',
          created_at: new Date().toISOString(),
          isDemo: true
        },
        {
          id: '802e1af7-57fd-4959-b298-6d065c9c730b',
          env_id: 'test_environment',
          tenant_id: 'test_tenant',
          version: 2,
          created_username: 'tanaka',
          created_at: new Date().toISOString(),
          isDemo: true
        },
        {
          id: '5521032f-41c7-4c30-bd03-1f82fc23e784',
          env_id: 'test_environment',
          tenant_id: 'test_tenant',
          version: 3,
          created_username: 'tanaka',
          created_at: new Date().toISOString(),
          isDemo: true
        }
      ]
      this.swapHistoryPageTotal = 4
      this.swapHistoryTotalCount = 3
    },
    addDeploymentLogsDemoData() {
      this.originalDeploymentLogs = {
        deploymentLogs: this.deploymentLogs,
        deployLogTotal: this.deployLogTotal
      }
      this.deploymentLogs = [
        {
          error: null,
          job_id: 'XXXXX-XXXX-XXXX-XXXX-XXXX',
          level: 'INFO',
          message: 'Deploy Done. Processed Time: 11.273329878000368 second(s).',
          source_env_id: 'test_environment',
          target_env_id: 'product_environment',
          tenant_id: 'test_tenant',
          timestamp: '2025-06-12T00:58:36.773423Z',
          isDemo: true
        },
        {
          error: null,
          job_id: 'XXXXX-XXXX-XXXX-XXXX-XXXX',
          level: 'WARNING',
          message: 'Deploy Done. Processed Time: 11.273329878000368 second(s).',
          source_env_id: 'test_environment',
          target_env_id: 'product_environment',
          tenant_id: 'test_tenant',
          timestamp: '2025-06-12T00:58:36.773423Z',
          isDemo: true
        },
        {
          error: 'エラーが発生しました。',
          job_id: 'XXXXX-XXXX-XXXX-XXXX-XXXX',
          level: 'ERROR',
          message: 'Failed to deploy.',
          source_env_id: 'test_environment',
          target_env_id: 'product_environment',
          tenant_id: 'test_tenant',
          timestamp: '2025-06-12T00:58:36.773423Z',
          isDemo: true
        }
      ]
      this.deployLogTotal = 3
    },
    removeDeploymentHistoriesDemoData() {
      if (this.originalDeploymentHistories) {
        this.deploymentHistories = this.originalDeploymentHistories.deploymentHistories
        this.deploymentHistoryPageTotal = this.originalDeploymentHistories.deploymentHistoryPageTotal
        this.deploymentHistoryTotalCount = this.originalDeploymentHistories.deploymentHistoryTotalCount
      } else {
        this.deploymentHistories = this.deploymentHistories.filter((item: any) => !item.isDemo)
      }
    },
    removeSwapHistoriesDemoData() {
      if (this.originalSwapHistories) {
        this.swapHistories = this.originalSwapHistories.swapHistories
        this.swapHistoryPageTotal = this.originalSwapHistories.swapHistoryPageTotal
        this.swapHistoryTotalCount = this.originalSwapHistories.swapHistoryTotalCount
      } else {
        this.swapHistories = this.swapHistories.filter((item: any) => !item.isDemo)
      }
    },
    removeDeploymentLogsDemoData() {
      if (this.originalDeploymentLogs) {
        this.deploymentLogs = this.originalDeploymentLogs.deploymentLogs
        this.deployLogTotal = this.originalDeploymentLogs.deployLogTotal
        this.originalDeploymentLogs = null
      } else {
        this.deploymentLogs = this.deploymentLogs.filter((item: any) => !item.isDemo)
        this.deployLogTotal = this.deploymentLogs.length
      }
    }
  }
})
