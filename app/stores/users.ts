export const useUsersStore = defineStore('usersStore', {
  persist: {
    pick: [],
    storage: window?.localStorage
  },
  state: () => ({
    users: [] as any[],
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    usersPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    usersTotal: 0,
    usersFilter: {} as Record<string, any>,
    selectedUsers: [] as any[],
    userModifyForm: false,
    userAccessLogModal: false,
    // for demo data backup
    originalUsers: null as any
  }),
  getters: {},
  actions: {
    resetFilters() {
      this.usersFilter = {}
    },
    async fetchAllUsers(tenant_id: string) {
      const authStore = useAuthStore()
      try {
        this.loadings.fetchAllUsers = true
        this.errors.fetchAllUsers = null
        const url = authStore.isOperator
          ? `/v2/users/all/tenants/${tenant_id}`
          : '/v2/users/all'

        const params = {
          page: this.usersPagination.page,
          page_size: this.usersPagination.pageCount,
          order: this.usersPagination.asc
        } as Record<string, any>
        if (this.usersFilter?.username) {
          params.username = this.usersFilter?.username
        }
        if (this.usersFilter?.status) {
          params.user_type = this.usersFilter?.status.value
        }
        const response = await useAPI().adminService.get(url,
          {
            params
          }
        )
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.usersTotal = pagination?.total_count
        this.users = response.data?.users || []
        return true
      } catch (error: any) {
        this.errors.fetchAllUsers = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchAllUsers = false
      }
    },
    async createUser(tenant_id: string, env_id: string, payload: any) {
      const authStore = useAuthStore()
      try {
        this.loadings.createUser = true
        this.errors = {}
        const url = authStore.isOperator
          ? `/v2/users/tenants/${tenant_id}`
          : '/v2/users'
        const response = await useAPI().adminService.post(url, payload)
        this.users.unshift(response.data as any)
        return response.data
      } catch (error: any) {
        this.errors.createUser = error?.response?.data || error
        return false
      } finally {
        this.loadings.createUser = false
      }
    },
    async deleteUser(username: string, tenant_id: string) {
      const authStore = useAuthStore()
      try {
        this.loadings.deleteUser = true
        this.errors.deleteUser = null
        const url = authStore.isOperator ? `/v2/users/${username}/tenants/${tenant_id}` : `/v2/users/${username}`
        await useAPI().adminService.delete(url)
        this.users = this.users.filter(user => user.username !== username)
        return true
      } catch (error: any) {
        this.errors.deleteUser = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteUser = false
      }
    },
    async deleteManyUser(usernames: string[], tenant_id: string) {
      const authStore = useAuthStore()
      try {
        this.loadings.deleteManyUser = true
        this.errors.deleteManyUser = null
        const url = authStore.isOperator ? `/v2/users/delete/tenants/${tenant_id}` : `/v2/users/delete`
        const response = await useAPI().adminService.post(
          url,
          {
            usernames
          }
        )
        if (response.data?.success?.length > 0) {
          this.users = this.users.filter(user => !usernames.includes(user.username))
          this.selectedUsers = []
          return true
        } else {
          this.errors.deleteManyUser = response.data?.failure
          return false
        }
      } catch (error: any) {
        this.errors.deleteManyUser = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteManyUser = false
      }
    },
    async updateUser(username: string, tenant_id: string, payload: any) {
      const authStore = useAuthStore()
      try {
        this.loadings.updateUser = true
        this.errors = {}
        const url = authStore.isOperator
          ? `/v2/users/${username}/tenants/${tenant_id}`
          : '/v2/users'
        const response = await useAPI().adminService.put(url, {
          email: payload.email,
          display_name: payload.display_name
        })
        this.users = this.users.map((user) => {
          console.log('Current user:', user)
          console.log('Response data:', response.data)
          if (user.username === response.data.username) {
            return response.data
          }
          return user
        })
        return response.data
      } catch (error: any) {
        this.errors.updateUser = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateUser = false
      }
    },
    addUserDemoData() {
      this.originalUsers = {
        users: this.users,
        usersTotal: this.usersTotal
      }
      this.users = [
        {
          email: '<EMAIL>',
          display_name: 'デモユーザー1',
          tenant_id: 'demo-tenant-1',
          username: 'demo-user-1',
          user_type: 'staff',
          created_username: 'admin_user',
          updated_username: 'admin_user',
          created_at: '2025-06-12T15:27:33.046998+09:00',
          updated_at: '2025-06-12T15:27:33.046998+09:00',
          last_login_at: '2025-06-13T14:57:16.145173+09:00',
          isDemo: true
        },
        {
          email: '<EMAIL>',
          display_name: 'デモユーザー2',
          tenant_id: 'demo-tenant-1',
          username: 'demo-user-2',
          user_type: 'admin',
          created_username: 'admin_user',
          updated_username: 'admin_user',
          created_at: '2025-06-09T16:18:59.561496+09:00',
          updated_at: '2025-06-09T16:18:59.561496+09:00',
          last_login_at: '2025-06-11T18:49:46.767835+09:00',
          isDemo: true
        }
      ]
      this.usersTotal = 2
    },
    removeUserDemoData() {
      if (this.originalUsers) {
        this.users = this.originalUsers.users
        this.usersTotal = this.originalUsers.usersTotal
      } else {
        this.users = this.users.filter((item: any) => !item.isDemo)
      }
    }
  }
})
