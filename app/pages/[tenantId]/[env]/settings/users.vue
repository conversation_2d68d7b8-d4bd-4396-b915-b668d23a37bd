<script setup lang="ts">
// Define required permissions for this page
import { debounce } from 'lodash'

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_users']
})

const { selectedTenantId, selectedEnvId } = useApp()
const authStore = useAuthStore()
const { lockedUsers } = storeToRefs(authStore)
const { userAccessLogs, loadings: loadingsAccessLogs, userAccessLogPagination, usersAccessLogTotalCount, userAccessLogFilter } = storeToRefs(authStore)
const usersStore = useUsersStore()
const {
  loadings,
  users,
  errors,
  usersPagination,
  usersTotal,
  usersFilter,
  selectedUsers,
  userModifyForm,
  userAccessLogModal
} = storeToRefs(usersStore)
const confirm = useConfirm()
const toast = useToast()
authStore.getLockedUsers()
const isFormOpen = ref(userModifyForm)
const { PERMISSIONS } = useAppPermissions()
const fetchUserAccessLogs = async () => {
  return await authStore.getUserAccessLogs(selectedTenantId.value)
}

const onReleaseIplock = async (ipAddress: string) => {
  confirm.show({
    title: `IPアドレスロック解除の確認`,
    description: `IPアドレス「${ipAddress}」を解除しますか？`,
    confirmText: '解除',
    onConfirm: async () => {
      const response = await authStore.releaseIpLock(ipAddress)
      if (!response) {
        toast.add({
          id: 'error',
          title: 'エラー',
          description: 'IPアドレス解除に失敗しました。',
          color: 'red'
        })
      } else {
        toast.add({
          title: '解除完了',
          description: 'IPアドレスを解除しました。',
          color: 'green'
        })
        authStore.getUserAccessLogs(selectedTenantId.value)
      }
    }
  })
}

const newLabel = ref(null)
const onSubmit = async (data: any) => {
  if (selectedUser.value) {
    const result = await usersStore.updateUser(
      selectedUser.value.username,
      selectedTenantId.value,
      data
    )
    if (result) {
      isFormOpen.value = false
    }
  } else {
    const result = await usersStore.createUser(
      selectedTenantId.value,
      selectedEnvId.value,
      data
    )
    if (result) {
      await usersStore.fetchAllUsers(selectedTenantId.value)
      newLabel.value = result
      isFormOpen.value = false
      toast.add({
        title: '成功',
        description: 'ユーザを追加しました。',
        color: 'green'
      })
    }
  }
}
onUnmounted(() => {
  usersStore.resetFilters()
})

const isFilterEmpty = computed(() => {
  return Object.values(usersFilter.value).every(value => !value)
})
const refresh = () => {
  usersStore.fetchAllUsers(selectedTenantId.value)
  authStore.getLockedUsers()
}
const debounceRefresh = debounce(refresh, 1000)

watch(
  () => usersPagination.value.pageCount,
  () => {
    usersPagination.value.page = 1
  }
)

watch(
  () => usersPagination.value,
  () => {
    refresh()
  },
  { deep: true, immediate: true }
)

watch(
  [() => usersFilter.value.username, () => usersFilter.value.status, () => usersFilter.value.enabled],
  () => {
    debounceRefresh()
  }
)

const selectedUser = ref<any>(null)
const isUpdateMode = computed(() => Boolean(selectedUser.value))
const onCreate = () => {
  selectedUser.value = null
  isFormOpen.value = true
}

const onUpdate = (label: any) => {
  selectedUser.value = label
  isFormOpen.value = true
}

const onReleaseUserlock = (username: string) => {
  confirm.show({
    title: `ユーザーロック解除の確認`,
    description: `ユーザ「${username}」のロックを解除しますか？`,
    confirmText: '解除',
    onConfirm: async () => {
      const response = await authStore.releaseUserLock(username)
      if (!response) {
        toast.add({
          id: 'error',
          title: 'エラー',
          description: 'ユーザ解除に失敗しました。',
          color: 'red'
        })
      } else {
        toast.add({
          title: '解除完了',
          description: 'ユーザを解除しました。',
          color: 'green'
        })
        authStore.getLockedUsers()
      }
    }
  })
}
const onDelete = (label: any) => {
  confirm.show({
    title: `ユーザ削除の確認`,
    description: `ユーザ「${label.username || label.key}」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      const result = await usersStore.deleteUser(label.username, selectedTenantId.value)
      if (result) {
        await usersStore.fetchAllUsers(selectedTenantId.value)
      }
    }
  })
}

const onToggleUserStatus = async (user: any) => {
  confirm.show({
    title: 'ステータス変更',
    description: `このユーザのステータスを${
      user.enabled ? '無効' : '有効'
    }に変更しますか？`,
    confirmText: '変更',
    onConfirm: async () => {
      const result = await usersStore.updateUser(
        user.username,
        selectedTenantId.value,
        { ...user, enabled: !user.enabled }
      )
      if (result) {
        toast.add({
          title: '成功',
          description: 'ユーザのステータスを変更しました',
          color: 'green'
        })
      }
    }
  })
}
const onDeleteMany = () => {
  const usernames = selectedUsers.value.map(user => user.username)
  confirm.show({
    title: `ユーザ削除の確認`,
    description: `ユーザ「${usernames.join(', ')}」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      const response = await usersStore.deleteManyUser(
        usernames,
        selectedTenantId.value
      )
      if (!response) {
        toast.add({
          id: 'error',
          title: 'エラー',
          description: 'ユーザ削除に失敗しました。',
          color: 'red'
        })
      } else {
        await usersStore.fetchAllUsers(selectedTenantId.value)
      }
    }
  })
}

const isLogsModalOpen = ref(userAccessLogModal)
const selectedUsername = ref('')

const onViewLogs = (user: any) => {
  selectedUsername.value = user.username
  isLogsModalOpen.value = true
}
</script>

<template>
  <UDashboardPanelContent class="p-0">
    <BaseEmptyList
      v-if="!users.length && !loadings.fetchAllUsers && isFilterEmpty"
      icon="lets-icons:user-scan-duotone"
      text="まだユーザがいません"
      init-button-label="ユーザを追加"
      init-button
      @init="onCreate"
    />
    <UsersTable
      v-else
      v-model:selected-users="selectedUsers"
      data-tour="users"
      :users="users"
      :locked-users="lockedUsers"
      :loading="loadings.fetchAllUser"
      :users-pagination="usersPagination"
      :users-total="usersTotal"
      :users-filter="usersFilter"
      @edit="onUpdate"
      @delete="onDelete"
      @delete-many="onDeleteMany"
      @create="onCreate"
      @view-logs="onViewLogs"
      @release-user-lock="onReleaseUserlock"
      @toggle-user-status="onToggleUserStatus"
      @refresh="usersStore.fetchAllUsers(selectedTenantId)"
    >
      <template #actions>
        <PermissionGuard :permission="PERMISSIONS.CREATE_USER">
          <RoleBasedButton
            data-tour="user-create"
            label="ユーザ追加"
            icon="icomoon-free:user-plus"
            color="gray"
            size="sm"
            @click="onCreate"
          />
        </PermissionGuard>
      </template>
    </UsersTable>

    <UsersLogModal
      v-model="isLogsModalOpen"
      data-tour="user-access-log"
      :username="selectedUsername"
      :user-access-logs="userAccessLogs"
      :loadings="loadingsAccessLogs"
      :user-access-log-pagination="userAccessLogPagination"
      :users-access-log-total-count="usersAccessLogTotalCount"
      :user-access-log-filter="userAccessLogFilter"
      :on-fetch-logs="fetchUserAccessLogs"
      @release-user="onReleaseUserlock"
      @release-ip="onReleaseIplock"
    />

    <UDashboardModal
      v-model="isFormOpen"
      data-tour="user-modal"
      :title="selectedUser ? 'ユーザを編集' : '新規ユーザ登録'"
      :description="
        selectedUser
          ? 'ユーザの情報を編集します。'
          : '新しいユーザを作成します。'
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <!-- ~/components/settings/MembersForm.vue -->
      <UserForm
        :users-pagination="usersPagination"
        :loading="loadings.createUser || loadings.updateUser"
        v-bind="selectedUser || {}"
        :is-update-mode="isUpdateMode"
        :error="errors.createUser || errors.updateUser"
        @close="isFormOpen = false"
        @submit="onSubmit"
      />
    </UDashboardModal>
  </UDashboardPanelContent>
</template>
