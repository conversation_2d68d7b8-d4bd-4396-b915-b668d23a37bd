<script setup lang="ts">
import { z } from 'zod'
import { debounce } from 'lodash-es'

definePageMeta({
  middleware: ['authentication', 'role-guard']
})

const { selectedTenantId, selectedEnvId } = useApp()
const settingsStore = useSettingsStore()
const { customSettings, loadings, errors } = storeToRefs(settingsStore)

const schema = z.object({
  maintenance_chatbot: z.boolean().optional(),
  maintenance_chatbot_message: z.string().optional()
})

type Schema = z.output<typeof schema>

const isInitialLoading = ref(true)

// Check if custom settings exist
const customSettingExist = computed(() => {
  return customSettings.value[selectedTenantId.value]?.[selectedEnvId.value] !== null
    && customSettings.value[selectedTenantId.value]?.[selectedEnvId.value] !== undefined
})

const isLoading = computed(() => {
  return loadings.value.fetchCustomSettings || isInitialLoading.value
})

// Form state
const state = reactive({
  maintenance_chatbot: false,
  maintenance_chatbot_message: 'システムメンテナンス中です。しばらくお待ちください。'
})

// Watch for changes in custom settings and update state
watch(
  () => customSettings.value[selectedTenantId.value]?.[selectedEnvId.value],
  (newSettings) => {
    if (newSettings) {
      state.maintenance_chatbot = newSettings.maintenance_chatbot || false
      state.maintenance_chatbot_message = newSettings.maintenance_chatbot_message || 'システムメンテナンス中です。しばらくお待ちください。'
    }
  },
  { immediate: true }
)

const updatedSuccess = ref({} as Record<string, boolean>)

const onUpdateCustomSettings = debounce(async (key: string, value: any) => {
  const result = await settingsStore.updateCustomSetting(
    selectedTenantId.value,
    selectedEnvId.value,
    key,
    value
  )
  if (result) {
    updatedSuccess.value[key] = true
    setTimeout(() => {
      updatedSuccess.value[key] = false
    }, 2000)
  }
}, 1000)

const validate = (state: any): FormError[] => {
  const errors = []
  return errors
}

const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  // This form auto-saves, so no submit action needed
}

const onInitSetting = async () => {
  if (!customSettingExist.value) {
    await settingsStore.createCustomSetting(selectedTenantId.value, selectedEnvId.value, {
      settings: {
        maintenance_chatbot: false,
        maintenance_chatbot_message: 'システムメンテナンス中です。しばらくお待ちください。'
      }
    })
  }
}

// Load settings on mount
onMounted(async () => {
  await settingsStore.fetchCustomSettings(selectedTenantId.value, selectedEnvId.value)
  isInitialLoading.value = false
})

// UI classes
const classNames = {
  formGroup: 'grid grid-cols-2 gap-2 items-center'
}

const ui = {
  formGroup: { container: '' }
}
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <!-- Loading state -->
    <div
      v-if="isLoading"
      class="space-y-4"
    >
      <div class="flex items-center justify-between mb-4">
        <div>
          <USkeleton class="h-6 w-48 mb-2" />
          <USkeleton class="h-4 w-64" />
        </div>
        <USkeleton class="h-9 w-10" />
      </div>

      <div class="space-y-6">
        <UFormGroup
          v-for="field in 3"
          :key="field"
          class="!items-baseline w-full mt-10"
        >
          <template #label>
            <USkeleton class="h-4 w-32 mb-2" />
          </template>
          <template #description>
            <USkeleton class="h-3 w-48 mb-3" />
          </template>
          <USkeleton class="h-9 w-full" />
        </UFormGroup>
      </div>
    </div>

    <!-- Content when loaded -->
    <UForm
      v-else-if="customSettingExist"
      :state="state"
      :validate="validate"
      :validate-on="['submit']"
      @submit="onSubmit"
    >
      <UDashboardSection
        title="メンテナンス設定"
        description="チャットボットのメンテナンス設定を行います。"
      >
        <UFormGroup
          id="settings-maintenance-chatbot"
          name="maintenance_chatbot"
          label="チャットボットメンテナンス"
          description="チャットボットのメンテナンスモードを有効にします。"
          :class="classNames.formGroup"
          :ui="ui.formGroup"
        >
          <UToggle
            v-model="state.maintenance_chatbot"
            size="md"
            @update:model-value="onUpdateCustomSettings('maintenance_chatbot', $event)"
          />
        </UFormGroup>

        <UFormGroup
          id="settings-maintenance-chatbot-message"
          name="maintenance_chatbot_message"
          label="メンテナンスメッセージ"
          description="メンテナンス中にユーザーに表示するメッセージを設定します。"
          :class="classNames.formGroup"
          :ui="ui.formGroup"
          class="!items-baseline"
        >
          <UTextarea
            v-model="state.maintenance_chatbot_message"
            :rows="3"
            placeholder="システムメンテナンス中です。しばらくお待ちください。"
            @input="onUpdateCustomSettings('maintenance_chatbot_message', $event.target.value)"
          />
        </UFormGroup>
      </UDashboardSection>
    </UForm>

    <!-- Empty state -->
    <BaseEmptyList
      v-else-if="!isLoading"
      icon="material-symbols:build-outline"
      text="メンテナンス設定がありません"
      init-button
      init-button-label="初期設定"
      @init="onInitSetting"
    />
  </UDashboardPanelContent>
</template>
