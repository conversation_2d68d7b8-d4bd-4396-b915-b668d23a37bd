<script setup lang="ts">
import { debounce } from 'lodash'
import { _height } from '#tailwind-config/theme'
import { BaseStatusToggleNoConfirm } from '#components'

const confirm = useConfirm()
const searchIndexersStore = useSearchIndexersStore()
const { loadings: searchIndexerLoadings, indexerStatus }
  = storeToRefs(searchIndexersStore)
const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()

const route = useRoute()
const router = useRouter()
const { trainingDataNavigators } = useNavigators()

const trainingDatasStore = useTrainingDatasStore()
const toursStore = useToursStore()
const {
  seletedKnowledges,
  trainingDataDetailRows,
  loadings: loadingsTrainingDatasStore,
  selectedTrainingData,
  trainingDataDetailPagination,
  trainingDataDetailFilter,
  hasAnyUnreflected,
  trainingDataDetailsTotal,
  trainingDataDetail,
  trainingDatas
} = storeToRefs(trainingDatasStore)

const showBulkUpdateModal = ref(false)

const breadcrumb = computed(() => {
  const _selectedTrainingData = trainingDatas.value.find(
    trainingData => trainingData.id === route.params.id
  )

  return [
    {
      label: trainingDataNavigators.value[0].label,
      icon: trainingDataNavigators.value[0].icon,
      to: `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/`
    },
    {
      label: _selectedTrainingData?.name,
      icon: contextTypeIcon(_selectedTrainingData?.original_context_type)
    },
    {
      label: 'ナレッジ一覧',
      icon: 'carbon:ibm-knowledge-catalog'
    }
  ]
})

const defaultColumns = [
  {
    key: 'content',
    label: 'ナレッジ',
    sortable: false
  },
  {
    key: 'created_at',
    label: '登録日時',
    sortable: false
  },
  {
    key: 'updated_at',
    label: '更新日時',
    sortable: false
  },
  {
    key: 'priority',
    label: '優先度',
    sortable: false,
    class: 'min-w-28'
  },
  {
    key: 'label',
    label: 'ラベル',
    sortable: false
  },
  {
    key: 'enabled',
    label: 'ステータス',
    sortable: false,
    class: 'w-36'
  }
]

const q = ref('')
const selectedColumns = ref(defaultColumns)
const selectedStatuses = ref([])
const sort = computed({
  get: () => ({
    column: 'id',
    direction: trainingDataDetailPagination.value.asc ? 'asc' : 'desc'
  }),
  set: (value) => {
    trainingDataDetailPagination.value.asc = value.direction === 'asc'
  }
})
const input = ref<{ input: HTMLInputElement }>()
const isNewUserModalOpen = ref(false)

const columns = computed(() =>
  defaultColumns.filter(column => selectedColumns.value.includes(column))
)

const defaultStatuses = [
  { label: '有効', value: 'enabled' },
  { label: '無効', value: 'disabled' }
]
const processStatus = [
  { label: 'まだ反映', value: 0 },
  { label: '更新中', value: 1 },
  { label: '反映ずみ', value: 2 }
]

function onSelect(row: any) {
  router.push(
    `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/${row.document_id}/${row.id}`
  )
}

defineShortcuts({
  '/': () => {
    input.value?.input?.focus()
  }
})

const exportKnowledgesToCSV = (rows: any[], documentName?: string) => {
  trainingDatasStore.exportKnowledgesToCSV(rows, documentName)
}

const onDeleteKnowledge = (rows: any) => {
  confirm.show({
    title: '削除確認',
    description: 'このナレッジを削除しますか？',
    confirmText: '削除',
    onConfirm: async () => {
      await trainingDatasStore.deleteKnowledgeContent(rows)
    }
  })
}

const onToggleStatusKnowledge = (row: any) => {
  confirm.show({
    title: 'ステータス変更確認',
    description: 'このナレッジのステータスを変更しますか？',
    confirmText: '変更',
    onConfirm: async () => {
      await trainingDatasStore.bulkUpdateKnowledges([row], {
        enabled: !row.enabled
      })
      trainingDatasStore.fetchTrainingDataDetail(
        route.params.id as string,
        selectedTenantId.value,
        selectedEnvId.value
      )
    }
  })
}

const items = [
  [
    {
      label: '一括編集',
      icon: 'carbon:batch-job',
      click: () => (showBulkUpdateModal.value = true),
      disabled: isSelectedEnvIsProd.value
    }
  ],
  [
    {
      label: 'CSV出力',
      icon: 'ph:file-csv-light',
      click: () =>
        exportKnowledgesToCSV(
          seletedKnowledges.value,
          selectedTrainingData.value?.name
        )
    }
  ],
  [
    {
      label: '削除',
      icon: 'i-heroicons-trash-20-solid',
      click: () => onDeleteKnowledge(seletedKnowledges.value),
      disabled: isSelectedEnvIsProd.value
    }
  ]
]

const rowMenus = (row: any) => {
  return [
    [
      {
        label: '編集',
        icon: 'tabler:edit',
        click: () => onSelect(row),
        disabled: isSelectedEnvIsProd.value
      },
      {
        label: 'CSV出力',
        icon: 'ph:file-csv-light',
        click: () => exportKnowledgesToCSV([row], row?.blob_path)
      }
    ],
    [
      {
        label: row?.enabled ? '無効化' : '有効化',
        icon: 'ri:toggle-line',
        click: () => onToggleStatusKnowledge(row),
        disabled: isSelectedEnvIsProd.value
      },
      {
        label: '削除',
        icon: 'i-heroicons-trash-20-solid',
        class: 'text-red-500 dark:text-red-400',
        iconClass: 'text-red-500 dark:text-red-400',
        click: () => onDeleteKnowledge([row]),
        disabled: isSelectedEnvIsProd.value
      }
    ]
  ]
}

onMounted(() => {
  trainingDataDetailPagination.value.page = 1
  // trainingDatasStore.fetchTrainingDataDetail(
  //   route.params.id as string,
  //   selectedTenantId.value,
  //   selectedEnvId.value
  // )

  // Check if we need to start the knowledge list tour
  if (route.query.tour === 'knowledge-list') {
    nextTick(() => {
      toursStore.showKnowledgeListTour()
    })
  }
})

const onRunIndexer = async () => {
  await searchIndexersStore.runIndexer(
    selectedTenantId.value,
    selectedEnvId.value
  )
}

const refresh = () => {
  if (!route.query.tour) {
    trainingDatasStore.fetchTrainingDataDetail(
      route.params.id as string,
      selectedTenantId.value,
      selectedEnvId.value,
      q.value // Pass the search query
    )
  }
}
const debounceRefresh = debounce(refresh, 1000)

watch(
  () => trainingDataDetailFilter,
  () => {
    debounceRefresh()
  },
  { deep: true }
)

watch(
  () => trainingDataDetailPagination.value,
  () => {
    refresh()
  },
  { deep: true, immediate: true }
)

// watch indexerUpdateHistoriesPagination.value.pageCount, if it is changed, reset page to 1
watch(
  () => trainingDataDetailPagination.value.pageCount,
  () => {
    trainingDataDetailPagination.value.page = 1
  }
)

// watch search query, debounceRefresh
watch(
  () => q.value,
  () => {
    debounceRefresh()
  }
)

const getTdClass = (row: any) => {
  return row && row.deleted ? 'line-through' : ''
}
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar
        data-tour="knowledge-navbar"
        :badge="trainingDataDetailRows.length"
      >
        <template #title>
          <UBreadcrumb :links="breadcrumb" />
        </template>
        <template #right>
          <!-- <UInput
            ref="input"
            v-model="q"
            icon="i-heroicons-funnel"
            autocomplete="off"
            placeholder="ナレッジの検索"
            class="hidden lg:block"
            @keydown.esc="$event.target.blur()"
          >
            <template #trailing>
              <UKbd value="/" />
            </template>
          </UInput> -->
          <!--
          <UButton
            label="ナレッジを追加"
            trailing-icon="i-heroicons-plus"
            color="gray"
            :to="`/${selectedTenantId}/${selectedEnvId}/training-data/${route.params.id}/new`"
          /> -->
        </template>
      </UDashboardNavbar>
      <UDashboardToolbar
        v-if="hasAnyUnreflected && indexerStatus !== 2"
        class="bg-orange-50 dark:bg-orange-100 text-gray-800 dark:text-gray-900"
      >
        <div class="flex items-center gap-1.5 text-sm">
          <UIcon name="si:warning-fill" />
          更新されたナレッジを適用するにはインデックスの更新が必要です。
        </div>
        <UButton
          icon="material-symbols:model-training"
          label="インデックスを更新"
          color="orange"
          variant="solid"
          size="sm"
          :loading="searchIndexerLoadings.runIndexer"
          @click="onRunIndexer"
        />
        <template #right />
      </UDashboardToolbar>
      <BaseRunIndexerProgress />
      <UDashboardToolbar>
        <template #left>
          <USelectMenu
            key="value"
            v-model="trainingDataDetailFilter.enabled"
            icon="i-heroicons-check-circle"
            placeholder="ステータス"
            multiple
            class="w-48"
            :options="defaultStatuses"
            by="value"
            :ui-menu="{ option: { base: 'capitalize' } }"
            data-tour="knowledge-status-filter"
          >
            <template #label>
              <div v-if="trainingDataDetailFilter.enabled?.length">
                {{
                  trainingDataDetailFilter.enabled
                    ?.map((obj: any) => obj.label)
                    .join(", ")
                }}
              </div>
              <div v-else>
                ステータス
              </div>
            </template>
          </USelectMenu>
          <LabelsSelect
            v-model="trainingDataDetailFilter.label"
            class="w-40"
          />

          <!-- processStatus -->
          <!-- <USelectMenu
            v-model="trainingDataDetailFilter.status"
            icon="i-heroicons-check-circle"
            :options="processStatus"
            option-attribute="label"
            class="w-40"
            placeholder="RAGステータス"
            :ui-menu="{ option: { base: 'capitalize' } }"
          >
            <template #option="{ option }">
              <span
                class="h-3 w-3 rounded-full"
                :class="`bg-${option.id}-500 dark:bg-${option.id}-400`"
              />
              <span  class="truncate">{{ $t(option.label) }}</span>
            </template>
          </USelectMenu> -->

          <!-- priorityMax -->
          <!-- <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">優先度高:</span>
            <BasePriorityInput
              v-model="trainingDataDetailFilter.priorityMax"
              class="w-20"
            />
          </div> -->

          <!-- priorityMin -->
          <!-- <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">優先度低:</span>
            <BasePriorityInput
              v-model="trainingDataDetailFilter.priorityMin"
              class="w-20"
            />
          </div> -->
        </template>

        <template #right>
          <div class="flex items-center gap-1.5">
            <div v-if="seletedKnowledges.length">
              <UDropdown
                :items="items"
                :popper="{ placement: 'bottom-start' }"
                data-tour="knowledge-bulk-actions"
              >
                <UButton
                  color="white"
                  :label="`一括操作（${seletedKnowledges.length}件）`"
                  icon="fluent:form-multiple-20-regular"
                  trailing-icon="i-heroicons-chevron-down-20-solid"
                  size="sm"
                />
              </UDropdown>
            </div>
            <UButton
              icon="prime:sync"
              color="gray"
              size="sm"
              data-tour="knowledge-refresh-button"
              @click="refresh"
            />
          </div>
        </template>
      </UDashboardToolbar>

      <UDashboardModal
        v-model="isNewUserModalOpen"
        title="New user"
        description="Add a new user to your database"
        :ui="{ width: 'sm:max-w-md' }"
      >
        <!-- ~/components/users/UsersForm.vue -->
        <UsersForm @close="isNewUserModalOpen = false" />
      </UDashboardModal>

      <UTable
        v-if="trainingDataDetail"
        v-model="seletedKnowledges"
        v-model:sort="sort"
        :rows="trainingDataDetail"
        sort-mode="manual"
        :columns="columns"
        :loading="loadingsTrainingDatasStore.fetchTrainingDataDetail"
        class="w-full"
        data-tour="knowledge-table"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #content-data="{ row }">
          <div
            :class="getTdClass(row)"
            class="cursor-pointer w-64"
            @click="onSelect(row)"
          >
            <div class="text-xs text-gray-500 dark:text-gray-500 truncate mb-1">
              {{ row.blob_path }}
            </div>
            <div
              class="cursor-pointer line-clamp-2 w-64 whitespace-break-spaces text-xs"
            >
              {{ row.content }}
            </div>
          </div>
        </template>
        <template #label-data="{ row }">
          <LabelsList :model-value="row.label" />
        </template>

        <template #enabled-data="{ row }">
          <div class="flex items-center gap-3 justify-between">
            <BaseStatusToggleNoConfirm
              v-model="row.enabled"
              class="capitalize flex-1 justify-center max-w-16"
              data-tour="knowledge-status-toggle"
              @toggle="onToggleStatusKnowledge(row)"
            />
            <UDropdown
              v-if="!row.deleted"
              class="group-hover:block"
              data-tour="knowledge-row-actions"
              :class="{
                block:
                  loadingsTrainingDatasStore['deleteKnowledgeContent']?.[
                    row.id
                  ],
                hidden:
                  !loadingsTrainingDatasStore['deleteKnowledgeContent']?.[
                    row.id
                  ]
              }"
              :items="rowMenus(row)"
              :popper="{ placement: 'bottom-start' }"
            >
              <UButton
                class="row-menu"
                color="white"
                icon="charm:menu-meatball"
                size="xs"
                square
                :loading="
                  loadingsTrainingDatasStore['deleteKnowledgeContent']?.[row.id]
                "
              />
            </UDropdown>
          </div>
        </template>

        <template #created_at-data="{ row }">
          <div>
            <div>
              {{ formatDateTime(row.created_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              作成者: {{ row.created_username }} ({{
                formatDistanceStrictDateTime(row.created_at)
              }})
            </div>
          </div>
        </template>

        <template #updated_at-data="{ row }">
          <div v-if="row.updated_at !== row.created_at">
            <div>
              {{ formatDateTime(row.updated_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              更新者: {{ row.updated_username }} ({{
                formatDistanceStrictDateTime(row.updated_at)
              }})
            </div>
          </div>
          <div v-else>
            --
          </div>
        </template>
      </UTable>
      <KnowledgeBulkUpdateModal
        :targets="seletedKnowledges"
        :show="showBulkUpdateModal"
        @close="showBulkUpdateModal = false"
      />
      <UDivider class="mt-0" />
      <UDashboardToolbar>
        <template #left>
          <div class="flex flex-wrap justify-between items-center">
            <div class="flex items-center gap-1.5">
              <span class="text-sm leading-5">表示件数:</span>

              <USelect
                v-model="trainingDataDetailPagination.pageCount"
                :options="[10, 20, 50, 100, 500, 1000]"
                class="w-20"
                size="xs"
              />
            </div>
            <UDivider
              class="mx-3 h-full py-1"
              orientation="vertical"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-if="
              trainingDataDetailPagination.pageCount < trainingDataDetailsTotal
            "
            v-model="trainingDataDetailPagination.page"
            :page-count="trainingDataDetailPagination.pageCount"
            :total="trainingDataDetailsTotal"
            size="sm"
          />
        </template>
      </UDashboardToolbar>
    </UDashboardPanel>
  </UDashboardPage>
</template>
