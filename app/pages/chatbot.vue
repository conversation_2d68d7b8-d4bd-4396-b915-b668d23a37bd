<script setup lang="ts">
definePageMeta({
  layout: 'liff'
})
const route = useRoute()
const newChatbotInstance = ref(null)

onMounted(() => {
  const { tenantId, env } = route.query
  console.log('LINE LIFF page loaded with params:', { tenantId, env })
  if (window?.LLMRagChatbot) {
    newChatbotInstance.value = new window.LLMRagChatbot.Class({
      tenantId,
      env
    })
  }
})
</script>

<template>
  <div class="flex flex-col items-center justify-center h-screen w-screen bg-gray-100 dark:bg-gray-900">
    <div
      v-if="!newChatbotInstance"
      class="text-center p-4"
    >
      <h1
        class="text-xl font-semibold text-primary-800 dark:text-primary-500 mb-2"
      >
        チャットページ
      </h1>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        チャットボットを読み込んでいます...
      </p>
    </div>
  </div>
</template>
