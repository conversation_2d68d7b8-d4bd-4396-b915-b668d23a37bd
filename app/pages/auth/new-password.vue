<script setup lang="ts">
definePageMeta({
  layout: 'auth'
})

useSeoMeta({
  title: 'Password Update'
})

const route = useRoute()

const fields = [
  {
    name: 'newPassword',
    label: 'パスワード',
    type: 'password',
    placeholder: '新しいパスワードを入力してください'
  },
  {
    name: 'confirmPassword',
    label: 'パスワードの確認',
    type: 'password',
    placeholder: '新しいパスワードを再度入力してください'
  }
]

const validate = (state: any) => {
  const errors = []
  if (!state.confirmPassword)
    errors.push({
      path: 'confirmPassword',
      message: 'パスワードは必須です'
    })
  if (!state.newPassword)
    errors.push({
      path: 'newPassword',
      message: 'パスワードは必須です'
    })
  if (state.newPassword !== state.confirmPassword)
    errors.push({
      path: 'confirmPassword',
      message: 'パスワードが一致しません'
    })

  return errors
}
const authStore = useAuthStore()
const { loadings, temp } = storeToRefs(authStore)

const toast = useToast()
async function onSubmit(data: any) {
  const result = await authStore.initPassword(data.newPassword)
  if (result) {
    navigateTo('/')
  } else {
    navigateTo('/auth/login')
    toast.add({
      id: 'error',
      title: 'エラー',
      description:
        'パスワードの設定に失敗しました。もう一度ログインしてください。',
      color: 'red'
    })
  }
}

const form = ref<any>(null)
const passwordRequirements = computed(() => {
  if (!form.value?.state) return []
  const { newPassword } = form.value?.state

  return [
    {
      label: '長さ：8文字以上',
      valid: newPassword?.length >= 8
    },
    {
      label: '英大文字（最低1文字）',
      valid: /[A-Z]/.test(newPassword)
    },
    {
      label: '英小文字（最低1文字）',
      valid:
        /[a-z]/.test(newPassword) && newPassword !== newPassword?.toUpperCase()
    },
    {
      label: '特殊文字（最低1文字）',
      valid: /[!@#$%^&*(),.?":{}|<>]/.test(newPassword)
    },
    {
      label: '数字（最低1文字）',
      valid: /[0-9]/.test(newPassword)
    }
  ]
})

onMounted(() => {
  const tour = route.query.tour

  if (!temp.value?.username && !tour) {
    navigateTo('/auth/login')
  }
})
</script>

<!-- eslint-disable vue/multiline-html-element-content-newline -->
<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
  <UCard
    data-tour="new-password-form"
    class="max-w-sm w-full bg-white/75 dark:bg-white/5 backdrop-blur"
  >
    <UAuthForm
      ref="form"
      :fields="fields"
      :validate="validate"
      title="パスワードの設定"
      align="top"
      icon="hugeicons:reset-password"
      :ui="{ base: 'text-center', footer: 'text-center' }"
      :submit-button="{
        trailingIcon: 'i-heroicons-arrow-right-20-solid',
        label: 'パスワードを設定'
      }"
      :loading="loadings.initPassword"
      @submit="onSubmit"
    >
      <template #description>
        <div class="text-sm text-left pt-4">
          初めてログインするため、新しいパスワードを設定してください。
        </div>
      </template>
      <template #newPassword-help>
        <div
          class="text-xs text-left pt-1"
          data-tour="password-requirements"
        >
          パスワードは下記の条件を満たす必要があります。
          <ul class="list-none pl-0">
            <li
              v-for="requirement in passwordRequirements"
              :key="requirement.label"
            >
              <span
                :class="[
                  requirement.valid ? 'text-green-600' : 'dark:text-gray-400'
                ]"
                class="flex items-center gap-1"
              >
                <UIcon
                  :name="
                    requirement.valid
                      ? 'i-heroicons-check-circle-solid'
                      : 'i-heroicons-x-circle-solid'
                  "
                  :class="[
                    requirement.valid ? 'text-green-600' : 'dark:text-red-400'
                  ]"
                />
                {{ requirement.label }}
              </span>
            </li>
          </ul>
        </div>
      </template>
      <template #footer>
        <div>PlayNext Lab @ {{ new Date().getFullYear() }}</div>
      </template>
    </UAuthForm>
  </UCard>
</template>
